/**
 * AI旅行云函数
 * 处理智能旅行规划相关的所有AI服务调用
 */

const cloud = require('wx-server-sdk')
const ZhipuAIService = require('./services/zhipu-ai')
const BaiduNLPService = require('./services/baidu-nlp')
const QWeatherService = require('./services/qweather')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

// 从环境变量获取API密钥（优先使用环境变量，提高安全性）
const ZHIPU_API_KEY = process.env.ZHIPU_API_KEY
const BAIDU_APP_ID = process.env.BAIDU_APP_ID
const BAIDU_API_KEY = process.env.BAIDU_API_KEY
const BAIDU_SECRET_KEY = process.env.BAIDU_SECRET_KEY

// 和风天气JWT配置（使用固定配置，避免环境变量换行问题）
const QWEATHER_KEY_ID = process.env.QWEATHER_KEY_ID || 'CFB4NPJKBG'
const QWEATHER_PROJECT_ID = process.env.QWEATHER_PROJECT_ID || '4JDXG7XUJ2'
const QWEATHER_PRIVATE_KEY = `-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIOt8XWOrzvLvRkTvLJk5kX71tMR2uWB5t3jogFs4A3fO
-----END PRIVATE KEY-----`

// 初始化服务
const zhipuService = new ZhipuAIService(ZHIPU_API_KEY)
const baiduService = new BaiduNLPService(BAIDU_APP_ID, BAIDU_API_KEY, BAIDU_SECRET_KEY)
const qweatherService = new QWeatherService(QWEATHER_KEY_ID, QWEATHER_PROJECT_ID, QWEATHER_PRIVATE_KEY)

exports.main = async (event, context) => {
  const { action, data } = event
  
  console.log('AI Travel Function Called:', { action, data })
  
  try {
    switch (action) {
      case 'parseTravel':
        return await parseTravel(data.text)
      
      case 'generateItinerary':
        return await generateItinerary(data.requirements)
      
      case 'extractLocations':
        return await extractLocations(data.text)
      
      case 'getWeather':
        return await getWeather(data.location, data.date)
      
      case 'optimizeRoute':
        return await optimizeRoute(data.locations)
      
      case 'testConnection':
        return await testConnection()
      
      default:
        return { 
          success: false, 
          message: `未知操作: ${action}` 
        }
    }
  } catch (error) {
    console.error('AI Travel Function Error:', error)
    return { 
      success: false, 
      message: error.message,
      error: error.stack 
    }
  }
}

/**
 * 解析旅行攻略文本
 */
async function parseTravel(text) {
  try {
    // 使用智谱AI解析旅行攻略
    const aiResult = await zhipuService.parseTravel(text)
    
    // 使用百度NLP提取地址信息
    const locations = await baiduService.extractAddresses(text)
    
    return {
      success: true,
      data: {
        aiParsed: aiResult,
        locations: locations,
        originalText: text,
        timestamp: new Date().toISOString()
      }
    }
  } catch (error) {
    console.error('Parse travel error:', error)
    return {
      success: false,
      message: '解析旅行攻略失败',
      error: error.message
    }
  }
}

/**
 * 智能生成行程
 */
async function generateItinerary(requirements) {
  try {
    const itinerary = await zhipuService.generateItinerary(requirements)
    
    return {
      success: true,
      data: {
        itinerary: itinerary,
        requirements: requirements,
        generatedAt: new Date().toISOString()
      }
    }
  } catch (error) {
    console.error('Generate itinerary error:', error)
    return {
      success: false,
      message: '生成行程失败',
      error: error.message
    }
  }
}

/**
 * 提取地点信息
 */
async function extractLocations(text) {
  try {
    const locations = await baiduService.extractAddresses(text)
    
    return {
      success: true,
      data: {
        locations: locations,
        count: locations.length
      }
    }
  } catch (error) {
    console.error('Extract locations error:', error)
    return {
      success: false,
      message: '提取地点信息失败',
      error: error.message
    }
  }
}

/**
 * 获取天气信息
 */
async function getWeather(location, date) {
  try {
    if (!QWEATHER_KEY_ID || !QWEATHER_PROJECT_ID || !QWEATHER_PRIVATE_KEY) {
      return {
        success: false,
        message: '和风天气配置未完成，请联系管理员'
      }
    }
    
    const weather = await qweatherService.getWeather(location, date)
    
    return {
      success: true,
      data: weather
    }
  } catch (error) {
    console.error('Get weather error:', error)
    return {
      success: false,
      message: '获取天气信息失败',
      error: error.message
    }
  }
}

/**
 * 优化路线
 */
async function optimizeRoute(locations) {
  try {
    // 简单的路线优化逻辑
    // 后续可以集成更复杂的算法
    const optimized = locations.sort((a, b) => {
      // 按照地理位置进行简单排序
      return (a.latitude + a.longitude) - (b.latitude + b.longitude)
    })
    
    return {
      success: true,
      data: {
        originalOrder: locations,
        optimizedOrder: optimized,
        optimizedAt: new Date().toISOString()
      }
    }
  } catch (error) {
    console.error('Optimize route error:', error)
    return {
      success: false,
      message: '路线优化失败',
      error: error.message
    }
  }
}

/**
 * 测试连接
 */
async function testConnection() {
  const results = {
    zhipu: false,
    baidu: false,
    qweather: false
  }
  
  try {
    // 测试智谱AI连接
    await zhipuService.testConnection()
    results.zhipu = true
  } catch (error) {
    console.error('Zhipu connection test failed:', error)
  }
  
  try {
    // 测试百度NLP连接
    await baiduService.testConnection()
    results.baidu = true
  } catch (error) {
    console.error('Baidu connection test failed:', error)
  }
  
  try {
    // 测试和风天气连接
    if (QWEATHER_KEY_ID && QWEATHER_PROJECT_ID && QWEATHER_PRIVATE_KEY) {
      await qweatherService.testConnection()
      results.qweather = true
    }
  } catch (error) {
    console.error('QWeather connection test failed:', error)
  }
  
  return {
    success: true,
    data: {
      connections: results,
      timestamp: new Date().toISOString()
    }
  }
}

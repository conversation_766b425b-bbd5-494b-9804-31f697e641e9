// cloud/functions/expense/index.js
// 记账相关云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 使用管理员权限的数据库实例
const db = cloud.database({
  throwOnNotFound: false,
})
const _ = db.command

exports.main = async (event, context) => {
  const { action, data, options } = event
  const { OPENID } = cloud.getWXContext()

  try {
    switch (action) {

      case 'getFinancialOverview':
        return await getFinancialOverview(OPENID)
      case 'getAllUserRecords':
        return await getAllUserRecords(OPENID)
      case 'testDatabaseAccess':
        return await testDatabaseAccess(OPENID)
      case 'addExpenseRecord':
        return await addExpenseRecord(OPENID, data)
      case 'getExpenseRecords':
        return await getExpenseRecords(OPENID, data)
      case 'deleteExpenseRecord':
        return await deleteExpenseRecord(OPENID, data)
      case 'deleteExpenseRecordsByPlanId':
        return await deleteExpenseRecordsByPlanId(OPENID, data)
      case 'getUserBudget':
        return await getUserBudget(OPENID)
      case 'updateUserBudget':
        return await updateUserBudget(OPENID, data)

      default:
        return {
          success: false,
          message: '未知操作类型'
        }
    }
  } catch (error) {
    return {
      success: false,
      message: error.message || '服务器错误'
    }
  }
}

// 获取财务概览
async function getFinancialOverview(openid) {
  try {
    const allRecords = await db.collection('expense_records')
      .where({ _openid: openid })
      .get()

    const records = allRecords
    
    // 计算总支出，确保amount字段正确处理
    const totalExpense = records.data.reduce((sum, record) => {
      const amount = Number(record.amount) || 0
      return sum + amount
    }, 0)

    // 修复分类逻辑：根据实际数据结构调整筛选条件
    const dailyExpense = records.data
      .filter(record => {
        // 使用正确的字段名：type='daily' 的记录归类为日常支出
        return record.type === 'daily' || (!record.type && record.record_type === 'expense')
      })
      .reduce((sum, record) => sum + (Number(record.amount) || 0), 0)

    // 简化旅行支出计算：只要type='travel'就算旅行支出
    const travelExpense = records.data
      .filter(record => record.type === 'travel')
      .reduce((sum, record) => sum + (Number(record.amount) || 0), 0)
    
    // 获取预算信息
    const budget = await getUserBudget(openid)
    let budgetData = { monthly: 5000, daily: 3000, travel: 2000 }
    if (budget.success && budget.data) {
      budgetData = budget.data
    }
    
    const totalBudget = budgetData.monthly || 5000
    const remainingBudget = Math.max(0, totalBudget - totalExpense)
    const usagePercentage = totalBudget > 0 ? (totalExpense / totalBudget) * 100 : 0
    const dailyPercentage = totalBudget > 0 ? (dailyExpense / totalBudget) * 100 : 0
    const travelPercentage = totalBudget > 0 ? (travelExpense / totalBudget) * 100 : 0
    
    const result = {
      totalExpense: Math.round(totalExpense * 100) / 100,
      dailyExpense: Math.round(dailyExpense * 100) / 100,
      travelExpense: Math.round(travelExpense * 100) / 100,
      totalBudget: Math.round(totalBudget * 100) / 100,
      remainingBudget: Math.round(remainingBudget * 100) / 100,
      usagePercentage: Math.round(usagePercentage * 100) / 100,
      dailyPercentage: Math.round(dailyPercentage * 100) / 100,
      travelPercentage: Math.round(travelPercentage * 100) / 100
    }
    
    return {
      success: true,
      data: result
    }
    
  } catch (error) {
    return {
      success: false,
      message: error.message || '获取财务概览失败',
      error: error.toString()
    }
  }
}

// {{ AURA-X: Modify - 简化数据库测试函数，移除过多调试信息. Approval: 寸止(ID:1738056000). }}
// 测试数据库访问权限
async function testDatabaseAccess(openid) {
  try {
    // 检查 expense_records 表是否存在并可访问
    const userRecords = await db.collection('expense_records')
      .where({ _openid: openid })
      .limit(5)
      .get()

    return {
      success: true,
      data: {
        tableExists: true,
        userRecordsCount: userRecords.data.length,
        sampleRecord: userRecords.data[0] || null
      }
    }

  } catch (error) {
    return {
      success: false,
      message: error.message,
      tableExists: false
    }
  }
}

// 获取用户所有记录（用于前端计算）
async function getAllUserRecords(openid) {
  try {
    const result = await db.collection('expense_records')
      .where({ _openid: openid })
      .orderBy('createTime', 'desc')
      .get()

    return {
      success: true,
      data: result.data
    }

  } catch (error) {
    return {
      success: false,
      message: error.message || '获取记录失败'
    }
  }
}

// 添加记账记录
async function addExpenseRecord(openid, recordData) {
  try {
    // 转换数据结构以匹配数据库期望的格式
    const record = {
      amount: recordData.amount,
      date: recordData.date,
      description: recordData.description || '',
      category: recordData.category,
      location: recordData.location,
      images: recordData.images || [],
      type: recordData.mode,                    // mode -> type ("daily" 或 "travel")
      record_type: recordData.type,             // type -> record_type ("expense" 或 "income")
      travel_plan_id: recordData.planId || null,        // planId -> travel_plan_id
      _openid: openid,
      createTime: new Date(),
      updateTime: new Date()
    }
    
    const result = await db.collection('expense_records').add({
      data: record
    })

    // 验证保存结果 - 重新查询刚保存的记录
    const savedRecord = await db.collection('expense_records').doc(result._id).get()

    return {
      success: true,
      data: { id: result._id },
      message: '记录添加成功'
    }

  } catch (error) {
    return {
      success: false,
      message: error.message || '添加记录失败'
    }
  }
}

// 获取记账记录列表
async function getExpenseRecords(openid, options = {}) {
  try {
    const { limit = 20, skip = 0, startDate, endDate, category, mode, planId, orderBy = 'createTime', orderDirection = 'desc' } = options

    let query = {}
    let participantOpenids = [openid] // 默认只查询当前用户

    // {{ AURA-X: Modify - 简化协作计划查询逻辑. Approval: 寸止(ID:1738056000). }}
    // 如果指定了planId，获取该计划的所有参与者
    if (planId) {
      try {
        const planResult = await db.collection('travel_plans').doc(planId).get()
        if (planResult.data && planResult.data.collaboration?.enabled) {
          // 协作计划：获取所有参与者openid
          participantOpenids = [planResult.data._openid] // 创建者
          if (planResult.data.collaboration.collaborators) {
            participantOpenids.push(...planResult.data.collaboration.collaborators.map(c => c.openid))
          }
        }
      } catch (error) {
        // 获取计划信息失败，使用默认查询
      }
      query.travel_plan_id = planId
    }

    // 设置用户查询条件
    query._openid = _.in(participantOpenids)

    // 时间筛选
    if (startDate && endDate) {
      query.createTime = _.gte(new Date(startDate)).and(_.lte(new Date(endDate)))
    }

    // 分类筛选
    if (category) {
      query['category.main'] = category
    }

    // {{ AURA-X: Modify - 修复模式字段名. Approval: 寸止(ID:1738056000). }}
    // 模式筛选 - 使用正确的字段名
    if (mode) {
      query.type = mode
    }

    // {{ AURA-X: Modify - 使用lookup关联用户信息. Approval: 寸止(ID:1738056000). }}
    // 执行聚合查询，关联用户信息
    const result = await db.collection('expense_records')
      .aggregate()
      .match(query)
      .lookup({
        from: 'users',
        localField: '_openid',
        foreignField: '_openid',
        as: 'userInfo'
      })
      .addFields({
        creatorName: {
          $ifNull: [
            { $arrayElemAt: ['$userInfo.nickName', 0] },
            { $arrayElemAt: ['$userInfo.nickname', 0] }
          ]
        },
        creatorAvatar: {
          $ifNull: [
            { $arrayElemAt: ['$userInfo.avatarUrl', 0] },
            { $arrayElemAt: ['$userInfo.avatar', 0] }
          ]
        }
      })
      .project({
        userInfo: 0 // 移除临时字段
      })
      .sort({ [orderBy]: orderDirection === 'desc' ? -1 : 1 })
      .limit(limit)
      .skip(skip)
      .end()

    return {
      success: true,
      data: result.list || [],
      total: result.list ? result.list.length : 0
    }

  } catch (error) {
    return {
      success: false,
      message: error.message || '获取记录失败'
    }
  }
}

// 删除记账记录
async function deleteExpenseRecord(openid, data) {
  try {
    const { recordId } = data

    if (!recordId) {
      return {
        success: false,
        message: '记录ID不能为空'
      }
    }

    console.log('删除记录请求:', { recordId, openid })

    // 首先查找记录（不限制openid）
    let recordExists = false
    let recordInfo = null
    let tableUsed = null

    // 检查新表中是否存在记录
    try {
      const checkResult = await db.collection('expense_records')
        .where({
          _id: recordId
        })
        .get()

      console.log('新表查询结果:', checkResult.data?.length || 0)

      if (checkResult.data && checkResult.data.length > 0) {
        recordExists = true
        recordInfo = checkResult.data[0]
        tableUsed = 'expense_records'
      }
    } catch (checkError) {
      console.log('新表查询失败:', checkError.message)
    }

    // 如果新表中没有，检查旧表
    if (!recordExists) {
      try {
        const oldCheckResult = await db.collection('records')
          .where({
            _id: recordId
          })
          .get()

        console.log('旧表查询结果:', oldCheckResult.data?.length || 0)

        if (oldCheckResult.data && oldCheckResult.data.length > 0) {
          recordExists = true
          recordInfo = oldCheckResult.data[0]
          tableUsed = 'records'
        }
      } catch (oldCheckError) {
        console.log('旧表查询失败:', oldCheckError.message)
      }
    }

    if (!recordExists) {
      console.log('记录不存在，recordId:', recordId)
      return {
        success: false,
        message: '记录不存在'
      }
    }

    console.log('找到记录，表:', tableUsed, '记录信息:', recordInfo)

    // 检查删除权限
    const hasDeletePermission = await checkDeletePermission(openid, recordInfo)
    if (!hasDeletePermission.success) {
      console.log('权限检查失败:', hasDeletePermission.message)
      return hasDeletePermission
    }

    // 执行删除操作
    let totalDeleted = 0

    if (tableUsed === 'expense_records') {
      // 从新表删除
      try {
        const newTableResult = await db.collection('expense_records')
          .where({
            _id: recordId
          })
          .remove()

        totalDeleted += newTableResult.stats.removed
        console.log('新表删除结果:', newTableResult.stats.removed)
      } catch (newTableError) {
        console.log('新表删除失败:', newTableError.message)
      }
    } else {
      // 从旧表删除
      try {
        const oldTableResult = await db.collection('records')
          .where({
            _id: recordId
          })
          .remove()

        totalDeleted += oldTableResult.stats.removed
        console.log('旧表删除结果:', oldTableResult.stats.removed)
      } catch (oldTableError) {
        console.log('旧表删除失败:', oldTableError.message)
      }
    }

    if (totalDeleted > 0) {
      return {
        success: true,
        message: '记录删除成功',
        deletedCount: totalDeleted
      }
    } else {
      return {
        success: false,
        message: '删除操作失败，请重试'
      }
    }

  } catch (error) {
    console.log('删除记录异常:', error)
    return {
      success: false,
      message: error.message || '删除记录失败，请重试'
    }
  }
}

// 检查删除权限
async function checkDeletePermission(openid, recordInfo) {
  try {
    // 如果是记录创建者，直接允许删除
    if (recordInfo._openid === openid) {
      return { success: true }
    }

    // 如果记录关联了旅行计划，检查协作权限
    const planId = recordInfo.travel_plan_id || recordInfo.planId
    if (planId) {
      try {
        const planResult = await db.collection('travel_plans').doc(planId).get()
        if (planResult.data) {
          const plan = planResult.data

          // 检查是否为计划创建者
          if (plan._openid === openid) {
            return { success: true }
          }

          // 检查是否为协作者且有编辑权限
          if (plan.collaboration?.enabled && plan.collaboration.collaborators) {
            const collaborator = plan.collaboration.collaborators.find(c => c.openid === openid)
            if (collaborator && collaborator.permissions?.includes('edit')) {
              return { success: true }
            }
          }
        }
      } catch (planError) {
        console.log('查询计划信息失败:', planError.message)
      }
    }

    return {
      success: false,
      message: '无权限删除此记录'
    }

  } catch (error) {
    console.log('权限检查异常:', error)
    return {
      success: false,
      message: '权限检查失败'
    }
  }
}

// {{ AURA-X: Modify - 修复字段名不一致和兼容性问题. Approval: 寸止(ID:1738056000). }}
// 根据旅行计划ID删除相关费用记录
async function deleteExpenseRecordsByPlanId(openid, data) {
  try {
    const { planId } = data

    if (!planId) {
      return {
        success: false,
        message: '计划ID不能为空'
      }
    }

    let totalDeleted = 0

    // 删除新表 expense_records 中的记录
    try {
      const newTableResult = await db.collection('expense_records')
        .where({
          travel_plan_id: planId,  // 新表使用 travel_plan_id
          _openid: openid
        })
        .remove()

      totalDeleted += newTableResult.stats.removed
    } catch (newTableError) {
      // 新表删除失败，继续尝试旧表
    }

    // 兼容性处理：同时清理旧表 records 中的数据
    try {
      const oldTableResult = await db.collection('records')
        .where({
          planId: planId,  // 旧表使用 planId
          _openid: openid,
          mode: 'travel'   // 只删除旅行模式的记录
        })
        .remove()

      totalDeleted += oldTableResult.stats.removed
    } catch (oldTableError) {
      // 旧表删除失败，可忽略
    }

    return {
      success: true,
      message: `成功删除 ${totalDeleted} 条相关费用记录`,
      deletedCount: totalDeleted
    }

  } catch (error) {
    return {
      success: false,
      message: error.message || '删除相关费用记录失败'
    }
  }
}

// 获取用户预算设置
async function getUserBudget(openid) {
  try {
    const result = await db.collection('user_budgets')
      .where({ _openid: openid })
      .get()
    
    if (result.data && result.data.length > 0) {
      return {
        success: true,
        data: result.data[0]
      }
    } else {
      // 返回默认预算
      return {
        success: true,
        data: {
          monthly: 5000,
          daily: 3000,
          travel: 2000
        }
      }
    }
    
  } catch (error) {
    return {
      success: false,
      message: error.message || '获取预算失败'
    }
  }
}

// 更新用户预算设置
async function updateUserBudget(openid, budgetData) {
  try {
    const data = {
      ...budgetData,
      _openid: openid,
      updateTime: new Date()
    }
    
    // 尝试更新，如果不存在则创建
    const existingBudget = await db.collection('user_budgets')
      .where({ _openid: openid })
      .get()
    
    let result
    if (existingBudget.data && existingBudget.data.length > 0) {
      // 更新现有记录
      result = await db.collection('user_budgets')
        .where({ _openid: openid })
        .update({
          data: data
        })
    } else {
      // 创建新记录
      data.createTime = new Date()
      result = await db.collection('user_budgets').add({
        data: data
      })
    }
    
    return {
      success: true,
      data: budgetData,
      message: '预算设置更新成功'
    }
    
  } catch (error) {
    return {
      success: false,
      message: error.message || '更新预算失败'
    }
  }
}


// 工具函数：获取时间范围
function getTimeRange(period) {
  const now = new Date()
  const start = new Date()

  switch (period) {
    case 'week':
      // 本周开始（周一）
      const dayOfWeek = now.getDay() || 7 // 周日为0，转换为7
      start.setDate(now.getDate() - dayOfWeek + 1)
      start.setHours(0, 0, 0, 0)
      break
    case 'month':
      // 本月开始（1号）
      start.setDate(1)
      start.setHours(0, 0, 0, 0)
      break
    case 'quarter':
      // 本季度开始
      const currentMonth = now.getMonth()
      const quarterStartMonth = Math.floor(currentMonth / 3) * 3
      start.setMonth(quarterStartMonth, 1)
      start.setHours(0, 0, 0, 0)
      break
    case 'year':
      // 本年开始（1月1日）
      start.setMonth(0, 1)
      start.setHours(0, 0, 0, 0)
      break
    default:
      // 默认本月
      start.setDate(1)
      start.setHours(0, 0, 0, 0)
  }

  return {
    start: start,
    end: now
  }
}

// 工具函数：获取上个周期的时间范围
function getPreviousTimeRange(period) {
  const now = new Date()
  const start = new Date()
  const end = new Date()
  
  switch (period) {
    case 'week':
      end.setDate(now.getDate() - 7)
      start.setDate(end.getDate() - 7)
      break
    case 'month':
      end.setMonth(now.getMonth() - 1)
      start.setMonth(end.getMonth() - 1)
      break
    case 'quarter':
      end.setMonth(now.getMonth() - 3)
      start.setMonth(end.getMonth() - 3)
      break
    case 'year':
      end.setFullYear(now.getFullYear() - 1)
      start.setFullYear(end.getFullYear() - 1)
      break
    default:
      end.setMonth(now.getMonth() - 1)
      start.setMonth(end.getMonth() - 1)
  }
  
  return {
    start: start,
    end: end
  }
}

// 工具函数：根据周期获取预算金额
function getBudgetAmount(budgetData, period) {
  switch (period) {
    case 'week':
      return (budgetData.monthly || 5000) / 4
    case 'month':
      return budgetData.monthly || 5000
    case 'quarter':
      return (budgetData.monthly || 5000) * 3
    case 'year':
      return (budgetData.monthly || 5000) * 12
    default:
      return budgetData.monthly || 5000
  }
}
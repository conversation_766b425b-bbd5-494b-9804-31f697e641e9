/**
 * 统一删除处理器
 * 解决安卓删除功能失效和跨平台兼容性问题
 */
class UnifiedDeleteHandler {
  constructor() {
    this.db = wx.cloud.database()
    this.isAndroid = this.detectAndroid()
  }

  /**
   * 检测是否为安卓平台
   */
  detectAndroid() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      return systemInfo.platform === 'android'
    } catch (error) {
      return false
    }
  }

  /**
   * 统一删除费用记录
   * @param {string} recordId 记录ID
   * @param {Object} options 选项
   */
  async deleteExpenseRecord(recordId, options = {}) {
    const { 
      showConfirm = true,
      confirmTitle = '确认删除',
      confirmContent = '确定要删除这条费用记录吗？',
      onSuccess = null,
      onError = null
    } = options

    try {
      // 1. 参数验证
      if (!recordId) {
        throw new Error('记录ID不能为空')
      }

      // 2. 确认删除（兼容性处理）
      if (showConfirm) {
        const confirmResult = await this.safeShowModal({
          title: confirmTitle,
          content: confirmContent,
          confirmText: '删除',
          confirmColor: '#ff4d4f'
        })
        
        if (!confirmResult) {
          return { success: false, cancelled: true }
        }
      }

      // 3. 显示加载状态
      this.showLoading('删除中...')

      // 4. 执行删除 - 使用云函数确保一致性
      const result = await this.callCloudFunction('expense', 'deleteExpenseRecord', {
        recordId
      })

      if (result.success) {
        this.showSuccess('删除成功')
        // 触觉反馈（安卓兼容）
        this.safeVibrate()
        
        // 执行成功回调
        if (onSuccess && typeof onSuccess === 'function') {
          try {
            await onSuccess(result)
          } catch (callbackError) {
            console.error('删除成功回调执行失败:', callbackError)
          }
        }
        
        return { success: true, data: result.data }
      } else {
        throw new Error(result.message || '删除失败')
      }

    } catch (error) {
      const errorMessage = error.message || '删除失败，请重试'
      this.showError(errorMessage)
      
      // 执行错误回调
      if (onError && typeof onError === 'function') {
        try {
          await onError(error)
        } catch (callbackError) {
          console.error('删除错误回调执行失败:', callbackError)
        }
      }
      
      return { success: false, error: errorMessage }
    } finally {
      this.hideLoading()
    }
  }

  /**
   * 删除旅行计划
   * @param {string} planId 计划ID
   * @param {Object} options 选项
   */
  async deleteTravelPlan(planId, options = {}) {
    const {
      showConfirm = true,
      confirmTitle = '确认删除',
      confirmContent = '删除后无法恢复，确定要删除这个旅行计划吗？',
      onSuccess = null,
      onError = null
    } = options

    try {
      // 1. 参数验证
      if (!planId) {
        throw new Error('计划ID不能为空')
      }

      // 2. 确认删除
      if (showConfirm) {
        const confirmResult = await this.safeShowModal({
          title: confirmTitle,
          content: confirmContent,
          confirmText: '删除',
          confirmColor: '#ff4d4f'
        })
        
        if (!confirmResult) {
          return { success: false, cancelled: true }
        }
      }

      // 3. 显示加载状态
      this.showLoading('删除中...')

      // 4. 先删除相关费用记录
      try {
        await this.callCloudFunction('expense', 'deleteExpenseRecordsByPlanId', {
          planId
        })
      } catch (expenseError) {
        console.error('删除相关费用记录失败:', expenseError)
        // 继续删除计划，但记录错误
      }

      // 5. 删除旅行计划
      const result = await this.callCloudFunction('travel', 'deleteTravelPlan', {
        planId
      })

      if (result.success) {
        this.showSuccess('删除成功')
        this.safeVibrate()
        
        // 执行成功回调
        if (onSuccess && typeof onSuccess === 'function') {
          try {
            await onSuccess(result)
          } catch (callbackError) {
            console.error('删除成功回调执行失败:', callbackError)
          }
        }
        
        return { success: true, data: result.data }
      } else {
        throw new Error(result.message || '删除失败')
      }

    } catch (error) {
      const errorMessage = error.message || '删除失败，请重试'
      this.showError(errorMessage)
      
      // 执行错误回调
      if (onError && typeof onError === 'function') {
        try {
          await onError(error)
        } catch (callbackError) {
          console.error('删除错误回调执行失败:', callbackError)
        }
      }
      
      return { success: false, error: errorMessage }
    } finally {
      this.hideLoading()
    }
  }

  /**
   * 批量删除费用记录
   * @param {Array} recordIds 记录ID数组
   * @param {Object} options 选项
   */
  async batchDeleteExpenseRecords(recordIds, options = {}) {
    const {
      showConfirm = true,
      confirmTitle = '批量删除',
      confirmContent = `确定要删除选中的 ${recordIds.length} 条记录吗？`,
      onProgress = null,
      onSuccess = null,
      onError = null
    } = options

    try {
      // 1. 参数验证
      if (!recordIds || !Array.isArray(recordIds) || recordIds.length === 0) {
        throw new Error('请选择要删除的记录')
      }

      // 2. 确认删除
      if (showConfirm) {
        const confirmResult = await this.safeShowModal({
          title: confirmTitle,
          content: confirmContent,
          confirmText: '删除',
          confirmColor: '#ff4d4f'
        })
        
        if (!confirmResult) {
          return { success: false, cancelled: true }
        }
      }

      // 3. 显示加载状态
      this.showLoading(`删除中 (0/${recordIds.length})`)

      // 4. 批量删除
      const results = []
      for (let i = 0; i < recordIds.length; i++) {
        const recordId = recordIds[i]
        
        try {
          const result = await this.callCloudFunction('expense', 'deleteExpenseRecord', {
            recordId
          })
          
          results.push({
            recordId,
            success: result.success,
            error: result.success ? null : result.message
          })
          
          // 更新进度
          this.showLoading(`删除中 (${i + 1}/${recordIds.length})`)
          
          // 进度回调
          if (onProgress && typeof onProgress === 'function') {
            try {
              await onProgress(i + 1, recordIds.length, result)
            } catch (callbackError) {
              console.error('进度回调执行失败:', callbackError)
            }
          }
          
        } catch (error) {
          results.push({
            recordId,
            success: false,
            error: error.message || '删除失败'
          })
        }
      }

      // 5. 统计结果
      const successCount = results.filter(r => r.success).length
      const failCount = results.length - successCount

      if (successCount > 0) {
        this.showSuccess(`成功删除 ${successCount} 条记录${failCount > 0 ? `，${failCount} 条失败` : ''}`)
        this.safeVibrate()
      } else {
        throw new Error('所有记录删除失败')
      }

      // 执行成功回调
      if (onSuccess && typeof onSuccess === 'function') {
        try {
          await onSuccess({ results, successCount, failCount })
        } catch (callbackError) {
          console.error('批量删除成功回调执行失败:', callbackError)
        }
      }

      return { 
        success: true, 
        data: { results, successCount, failCount }
      }

    } catch (error) {
      const errorMessage = error.message || '批量删除失败'
      this.showError(errorMessage)
      
      // 执行错误回调
      if (onError && typeof onError === 'function') {
        try {
          await onError(error)
        } catch (callbackError) {
          console.error('批量删除错误回调执行失败:', callbackError)
        }
      }
      
      return { success: false, error: errorMessage }
    } finally {
      this.hideLoading()
    }
  }

  /**
   * 安全的云函数调用
   * @param {string} name 云函数名
   * @param {string} action 操作
   * @param {Object} data 数据
   */
  async callCloudFunction(name, action, data) {
    try {
      const result = await wx.cloud.callFunction({
        name,
        data: { action, data }
      })

      if (result.result) {
        return result.result
      } else {
        throw new Error('云函数返回格式错误')
      }
    } catch (error) {
      // 安卓平台特殊处理
      if (this.isAndroid && error.errCode) {
        throw new Error(`操作失败 (${error.errCode}): ${error.errMsg || '请重试'}`)
      }
      throw error
    }
  }

  /**
   * 安全的模态框显示（安卓兼容）
   * @param {Object} options 选项
   */
  async safeShowModal(options) {
    return new Promise((resolve) => {
      try {
        // 安卓平台特殊处理
        const modalOptions = {
          ...options,
          success: (res) => {
            resolve(res.confirm)
          },
          fail: (error) => {
            console.error('showModal失败:', error)
            // 安卓平台降级处理
            if (this.isAndroid) {
              resolve(true) // 默认确认
            } else {
              resolve(false)
            }
          }
        }

        wx.showModal(modalOptions)
      } catch (error) {
        console.error('showModal异常:', error)
        resolve(false)
      }
    })
  }

  /**
   * 安全的触觉反馈
   */
  safeVibrate() {
    try {
      wx.vibrateShort()
    } catch (error) {
      // 安卓某些机型可能不支持，静默处理
      console.log('触觉反馈不支持:', error)
    }
  }

  /**
   * 统一的加载状态管理
   */
  showLoading(title) {
    try {
      wx.showLoading({ 
        title, 
        mask: true,
        // 安卓平台增加超时处理
        ...(this.isAndroid && { duration: 10000 })
      })
    } catch (error) {
      console.error('showLoading失败:', error)
    }
  }

  hideLoading() {
    try {
      wx.hideLoading()
    } catch (error) {
      console.error('hideLoading失败:', error)
    }
  }

  showSuccess(title) {
    try {
      wx.showToast({ 
        title, 
        icon: 'success',
        duration: 2000
      })
    } catch (error) {
      console.error('showToast失败:', error)
    }
  }

  showError(title) {
    try {
      wx.showToast({ 
        title, 
        icon: 'none',
        duration: 3000
      })
    } catch (error) {
      console.error('showToast失败:', error)
    }
  }
}

// 创建全局实例
const unifiedDeleteHandler = new UnifiedDeleteHandler()

export default unifiedDeleteHandler

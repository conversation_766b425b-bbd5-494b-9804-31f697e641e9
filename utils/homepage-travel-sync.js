/**
 * 首页旅行数据同步器
 * 专门负责首页旅行计划数据的获取和实时更新
 */
class HomepageTravelSync {
  constructor() {
    this.cache = new Map()
    this.isLoading = false
    this.lastUpdateTime = 0
    this.updateCallbacks = new Set()
    
    // 配置
    this.config = {
      cacheTimeout: 2 * 60 * 1000,  // 2分钟缓存
      maxRetries: 3,
      retryDelay: 1000
    }
  }

  /**
   * 注册更新回调
   * @param {Function} callback 更新回调函数
   */
  onUpdate(callback) {
    this.updateCallbacks.add(callback)
    
    // 如果有缓存数据，立即调用回调
    if (this.cache.has('travel_data')) {
      const cachedData = this.cache.get('travel_data')
      if (this.isCacheValid(cachedData.timestamp)) {
        try {
          callback(cachedData.data)
        } catch (error) {
          console.error('首页旅行数据回调执行失败:', error)
        }
      }
    }
  }

  /**
   * 移除更新回调
   * @param {Function} callback 更新回调函数
   */
  offUpdate(callback) {
    this.updateCallbacks.delete(callback)
  }

  /**
   * 获取首页旅行数据
   * @param {boolean} forceRefresh 强制刷新
   */
  async getTravelData(forceRefresh = false) {
    try {
      // 检查缓存
      if (!forceRefresh && this.cache.has('travel_data')) {
        const cachedData = this.cache.get('travel_data')
        if (this.isCacheValid(cachedData.timestamp)) {
          return {
            success: true,
            data: cachedData.data,
            fromCache: true
          }
        }
      }

      // 防止重复加载
      if (this.isLoading) {
        return await this.waitForLoading()
      }

      this.isLoading = true

      // 获取数据
      const result = await this.fetchTravelData()
      
      if (result.success) {
        // 缓存数据
        this.cache.set('travel_data', {
          data: result.data,
          timestamp: Date.now()
        })
        
        this.lastUpdateTime = Date.now()
        
        // 通知所有回调
        this.notifyCallbacks(result.data)
      }

      return result

    } catch (error) {
      console.error('获取首页旅行数据失败:', error)
      return {
        success: false,
        error: error.message,
        data: this.getDefaultData()
      }
    } finally {
      this.isLoading = false
    }
  }

  /**
   * 获取当前计划数据
   * @param {boolean} forceRefresh 强制刷新
   */
  async getCurrentPlan(forceRefresh = false) {
    try {
      // 检查缓存
      if (!forceRefresh && this.cache.has('current_plan')) {
        const cachedData = this.cache.get('current_plan')
        if (this.isCacheValid(cachedData.timestamp)) {
          return {
            success: true,
            data: cachedData.data,
            fromCache: true
          }
        }
      }

      // 获取当前计划
      const result = await wx.cloud.callFunction({
        name: 'travel',
        data: {
          action: 'getCurrentPlan',
          data: {}
        }
      })

      if (result.result && result.result.success) {
        const planData = result.result.data
        
        // 缓存数据
        this.cache.set('current_plan', {
          data: planData,
          timestamp: Date.now()
        })

        return {
          success: true,
          data: planData
        }
      } else {
        throw new Error(result.result?.message || '获取当前计划失败')
      }

    } catch (error) {
      console.error('获取当前计划失败:', error)
      return {
        success: false,
        error: error.message,
        data: null
      }
    }
  }

  /**
   * 获取旅行统计数据
   * @param {boolean} forceRefresh 强制刷新
   */
  async getTravelStats(forceRefresh = false) {
    try {
      // 检查缓存
      if (!forceRefresh && this.cache.has('travel_stats')) {
        const cachedData = this.cache.get('travel_stats')
        if (this.isCacheValid(cachedData.timestamp)) {
          return {
            success: true,
            data: cachedData.data,
            fromCache: true
          }
        }
      }

      // 获取统计数据
      const result = await wx.cloud.callFunction({
        name: 'travel',
        data: {
          action: 'getTravelStatistics',
          data: {}
        }
      })

      if (result.result && result.result.success) {
        const statsData = result.result.data
        
        // 缓存数据
        this.cache.set('travel_stats', {
          data: statsData,
          timestamp: Date.now()
        })

        return {
          success: true,
          data: statsData
        }
      } else {
        throw new Error(result.result?.message || '获取旅行统计失败')
      }

    } catch (error) {
      console.error('获取旅行统计失败:', error)
      return {
        success: false,
        error: error.message,
        data: this.getDefaultStats()
      }
    }
  }

  /**
   * 获取最近的旅行计划
   * @param {number} limit 限制数量
   * @param {boolean} forceRefresh 强制刷新
   */
  async getRecentPlans(limit = 3, forceRefresh = false) {
    try {
      const cacheKey = `recent_plans_${limit}`
      
      // 检查缓存
      if (!forceRefresh && this.cache.has(cacheKey)) {
        const cachedData = this.cache.get(cacheKey)
        if (this.isCacheValid(cachedData.timestamp)) {
          return {
            success: true,
            data: cachedData.data,
            fromCache: true
          }
        }
      }

      // 获取最近计划
      const result = await wx.cloud.callFunction({
        name: 'travel',
        data: {
          action: 'getTravelPlans',
          data: { 
            limit,
            orderBy: 'createTime',
            orderDirection: 'desc'
          }
        }
      })

      if (result.result && result.result.success) {
        const plansData = result.result.data || []
        
        // 缓存数据
        this.cache.set(cacheKey, {
          data: plansData,
          timestamp: Date.now()
        })

        return {
          success: true,
          data: plansData
        }
      } else {
        throw new Error(result.result?.message || '获取最近计划失败')
      }

    } catch (error) {
      console.error('获取最近计划失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  }

  /**
   * 批量获取首页所需的所有旅行数据
   * @param {boolean} forceRefresh 强制刷新
   */
  async fetchTravelData(forceRefresh = false) {
    try {
      // 并行获取所有数据
      const [currentPlanResult, statsResult, recentPlansResult] = await Promise.allSettled([
        this.getCurrentPlan(forceRefresh),
        this.getTravelStats(forceRefresh),
        this.getRecentPlans(3, forceRefresh)
      ])

      // 处理结果
      const travelData = {
        currentPlan: currentPlanResult.status === 'fulfilled' && currentPlanResult.value.success 
          ? currentPlanResult.value.data 
          : null,
        
        stats: statsResult.status === 'fulfilled' && statsResult.value.success 
          ? statsResult.value.data 
          : this.getDefaultStats(),
        
        recentPlans: recentPlansResult.status === 'fulfilled' && recentPlansResult.value.success 
          ? recentPlansResult.value.data 
          : [],
        
        lastUpdate: Date.now()
      }

      return {
        success: true,
        data: travelData
      }

    } catch (error) {
      console.error('批量获取旅行数据失败:', error)
      return {
        success: false,
        error: error.message,
        data: this.getDefaultData()
      }
    }
  }

  /**
   * 等待加载完成
   */
  async waitForLoading() {
    return new Promise((resolve) => {
      const checkLoading = () => {
        if (!this.isLoading) {
          if (this.cache.has('travel_data')) {
            const cachedData = this.cache.get('travel_data')
            resolve({
              success: true,
              data: cachedData.data,
              fromCache: true
            })
          } else {
            resolve({
              success: false,
              error: '加载失败',
              data: this.getDefaultData()
            })
          }
        } else {
          setTimeout(checkLoading, 100)
        }
      }
      checkLoading()
    })
  }

  /**
   * 检查缓存是否有效
   * @param {number} timestamp 时间戳
   */
  isCacheValid(timestamp) {
    return Date.now() - timestamp < this.config.cacheTimeout
  }

  /**
   * 通知所有回调
   * @param {Object} data 数据
   */
  notifyCallbacks(data) {
    this.updateCallbacks.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error('首页旅行数据回调执行失败:', error)
      }
    })
  }

  /**
   * 获取默认数据
   */
  getDefaultData() {
    return {
      currentPlan: null,
      stats: this.getDefaultStats(),
      recentPlans: [],
      lastUpdate: Date.now()
    }
  }

  /**
   * 获取默认统计数据
   */
  getDefaultStats() {
    return {
      totalPlans: 0,
      activePlans: 0,
      completedPlans: 0,
      totalExpenses: 0,
      totalBudget: 0
    }
  }

  /**
   * 清除缓存
   * @param {string} key 缓存键，不传则清除所有
   */
  clearCache(key = null) {
    if (key) {
      this.cache.delete(key)
    } else {
      this.cache.clear()
    }
  }

  /**
   * 强制刷新所有数据
   */
  async forceRefresh() {
    this.clearCache()
    return await this.getTravelData(true)
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      lastUpdate: this.lastUpdateTime,
      isLoading: this.isLoading,
      callbackCount: this.updateCallbacks.size
    }
  }
}

// 创建全局实例
const homepageTravelSync = new HomepageTravelSync()

export default homepageTravelSync

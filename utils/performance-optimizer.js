/**
 * 性能优化器
 * 提供批量数据加载、智能缓存、setData优化等功能
 */
class PerformanceOptimizer {
  constructor() {
    // 批量加载队列
    this.loadingQueue = new Map()
    
    // 批量setData队列
    this.setDataQueue = new Map()
    
    // 定时器管理
    this.timers = new Map()
    
    // 性能统计
    this.stats = {
      batchLoadCount: 0,
      batchSetDataCount: 0,
      cacheHitCount: 0,
      cacheMissCount: 0,
      totalLoadTime: 0,
      averageLoadTime: 0
    }
    
    // 配置
    this.config = {
      batchDelay: 16,           // 批量处理延迟（一帧时间）
      maxBatchSize: 10,         // 最大批量大小
      setDataDelay: 32,         // setData批量延迟
      maxRetries: 3,            // 最大重试次数
      retryDelay: 1000          // 重试延迟
    }
  }

  /**
   * 批量数据加载
   * @param {Array} requests 请求数组
   * @param {Object} options 选项
   */
  async batchLoad(requests, options = {}) {
    const startTime = Date.now()
    
    try {
      const {
        maxConcurrency = 5,
        timeout = 10000,
        retryOnError = true
      } = options

      // 分批处理请求
      const batches = this.chunkArray(requests, maxConcurrency)
      const results = []

      for (const batch of batches) {
        const batchPromises = batch.map(request => 
          this.executeRequest(request, { timeout, retryOnError })
        )
        
        const batchResults = await Promise.allSettled(batchPromises)
        results.push(...batchResults)
      }

      // 处理结果
      const processedResults = results.map((result, index) => ({
        key: requests[index].key,
        success: result.status === 'fulfilled',
        data: result.status === 'fulfilled' ? result.value : null,
        error: result.status === 'rejected' ? result.reason : null
      }))

      // 更新统计
      this.updateStats(startTime, processedResults)

      return {
        success: true,
        results: processedResults,
        successCount: processedResults.filter(r => r.success).length,
        failCount: processedResults.filter(r => !r.success).length
      }

    } catch (error) {
      console.error('批量加载失败:', error)
      return {
        success: false,
        error: error.message,
        results: []
      }
    }
  }

  /**
   * 执行单个请求
   * @param {Object} request 请求对象
   * @param {Object} options 选项
   */
  async executeRequest(request, options = {}) {
    const { timeout = 10000, retryOnError = true } = options
    const { type, action, data, cloudFunction } = request
    
    let retryCount = 0
    
    while (retryCount <= this.config.maxRetries) {
      try {
        // 设置超时
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), timeout)
        })
        
        // 执行云函数调用
        const requestPromise = wx.cloud.callFunction({
          name: cloudFunction || type,
          data: { action, data }
        })
        
        const result = await Promise.race([requestPromise, timeoutPromise])
        
        if (result.result && result.result.success) {
          return result.result
        } else {
          throw new Error(result.result?.message || '请求失败')
        }
        
      } catch (error) {
        retryCount++
        
        if (!retryOnError || retryCount > this.config.maxRetries) {
          throw error
        }
        
        // 等待后重试
        await this.delay(this.config.retryDelay * retryCount)
      }
    }
  }

  /**
   * 智能setData批量处理
   * @param {Object} pageInstance 页面实例
   * @param {Object} data 数据
   * @param {Function} callback 回调函数
   */
  batchSetData(pageInstance, data, callback = null) {
    if (!pageInstance || !pageInstance.setData) {
      console.error('无效的页面实例')
      return
    }

    const pageId = this.getPageId(pageInstance)
    
    // 合并数据到队列
    if (!this.setDataQueue.has(pageId)) {
      this.setDataQueue.set(pageId, {
        pageInstance,
        data: {},
        callbacks: []
      })
    }

    const queueItem = this.setDataQueue.get(pageId)
    Object.assign(queueItem.data, data)
    
    if (callback) {
      queueItem.callbacks.push(callback)
    }

    // 清除之前的定时器
    if (this.timers.has(`setData_${pageId}`)) {
      clearTimeout(this.timers.get(`setData_${pageId}`))
    }

    // 设置新的定时器
    const timer = setTimeout(() => {
      this.flushSetData(pageId)
    }, this.config.setDataDelay)
    
    this.timers.set(`setData_${pageId}`, timer)
  }

  /**
   * 立即执行批量setData
   * @param {string} pageId 页面ID
   */
  flushSetData(pageId) {
    const queueItem = this.setDataQueue.get(pageId)
    
    if (!queueItem || Object.keys(queueItem.data).length === 0) {
      return
    }

    try {
      // 执行setData
      queueItem.pageInstance.setData(queueItem.data, () => {
        // 执行所有回调
        queueItem.callbacks.forEach(callback => {
          try {
            callback()
          } catch (error) {
            console.error('setData回调执行失败:', error)
          }
        })
      })

      // 更新统计
      this.stats.batchSetDataCount++

    } catch (error) {
      console.error('批量setData执行失败:', error)
    } finally {
      // 清理队列
      this.setDataQueue.delete(pageId)
      this.timers.delete(`setData_${pageId}`)
    }
  }

  /**
   * 智能缓存策略
   * @param {string} dataType 数据类型
   * @param {number} lastUpdate 最后更新时间
   * @param {Object} options 选项
   */
  shouldUseCache(dataType, lastUpdate, options = {}) {
    const { forceRefresh = false, maxAge = 300000 } = options // 默认5分钟
    
    if (forceRefresh) {
      this.stats.cacheMissCount++
      return false
    }
    
    const cacheRules = {
      'user_info': 300000,      // 5分钟
      'travel_plans': 60000,    // 1分钟
      'expense_records': 0,     // 不缓存
      'analytics': 180000,      // 3分钟
      'current_plan': 30000,    // 30秒
      'ongoing_plans': 60000    // 1分钟
    }

    const ttl = cacheRules[dataType] || maxAge
    const shouldCache = Date.now() - lastUpdate < ttl
    
    if (shouldCache) {
      this.stats.cacheHitCount++
    } else {
      this.stats.cacheMissCount++
    }
    
    return shouldCache
  }

  /**
   * 并行数据加载优化
   * @param {Array} loadTasks 加载任务数组
   * @param {Object} options 选项
   */
  async parallelLoad(loadTasks, options = {}) {
    const {
      maxConcurrency = 3,
      failFast = false,
      timeout = 15000
    } = options

    try {
      // 分批并行执行
      const batches = this.chunkArray(loadTasks, maxConcurrency)
      const allResults = []

      for (const batch of batches) {
        const batchPromises = batch.map(task => 
          this.executeLoadTask(task, timeout)
        )
        
        if (failFast) {
          const batchResults = await Promise.all(batchPromises)
          allResults.push(...batchResults)
        } else {
          const batchResults = await Promise.allSettled(batchPromises)
          allResults.push(...batchResults.map(result => 
            result.status === 'fulfilled' ? result.value : { success: false, error: result.reason }
          ))
        }
      }

      return {
        success: true,
        results: allResults,
        successCount: allResults.filter(r => r.success).length
      }

    } catch (error) {
      return {
        success: false,
        error: error.message,
        results: []
      }
    }
  }

  /**
   * 执行加载任务
   * @param {Function} task 任务函数
   * @param {number} timeout 超时时间
   */
  async executeLoadTask(task, timeout) {
    try {
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('任务超时')), timeout)
      })
      
      const result = await Promise.race([task(), timeoutPromise])
      return { success: true, data: result }
      
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取页面ID
   * @param {Object} pageInstance 页面实例
   */
  getPageId(pageInstance) {
    return pageInstance.route || pageInstance.__route__ || 'unknown'
  }

  /**
   * 数组分块
   * @param {Array} array 数组
   * @param {number} size 块大小
   */
  chunkArray(array, size) {
    const chunks = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }

  /**
   * 延迟函数
   * @param {number} ms 毫秒
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 更新性能统计
   * @param {number} startTime 开始时间
   * @param {Array} results 结果数组
   */
  updateStats(startTime, results) {
    const loadTime = Date.now() - startTime
    this.stats.batchLoadCount++
    this.stats.totalLoadTime += loadTime
    this.stats.averageLoadTime = this.stats.totalLoadTime / this.stats.batchLoadCount
  }

  /**
   * 获取性能统计
   */
  getStats() {
    return {
      ...this.stats,
      cacheHitRate: this.stats.cacheHitCount / (this.stats.cacheHitCount + this.stats.cacheMissCount) || 0
    }
  }

  /**
   * 重置统计
   */
  resetStats() {
    this.stats = {
      batchLoadCount: 0,
      batchSetDataCount: 0,
      cacheHitCount: 0,
      cacheMissCount: 0,
      totalLoadTime: 0,
      averageLoadTime: 0
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    // 清除所有定时器
    this.timers.forEach(timer => clearTimeout(timer))
    this.timers.clear()
    
    // 清空队列
    this.loadingQueue.clear()
    this.setDataQueue.clear()
    
    console.log('性能优化器已清理')
  }
}

// 创建全局实例
const performanceOptimizer = new PerformanceOptimizer()

export default performanceOptimizer

/**
 * 实时同步管理器
 * 解决数据同步问题，确保旅行计划数据在首页和各页面实时更新
 */
class RealtimeSyncManager {
  constructor() {
    // 同步监听器映射
    this.syncListeners = new Map()
    
    // 同步定时器
    this.syncTimers = new Map()
    
    // 数据版本控制
    this.dataVersions = new Map()
    
    // 同步状态
    this.syncStatus = {
      isActive: false,
      lastSyncTime: 0,
      syncCount: 0
    }
    
    // 同步配置
    this.config = {
      syncInterval: 3000,      // 3秒同步间隔
      maxRetries: 3,           // 最大重试次数
      retryDelay: 1000,        // 重试延迟
      batchSize: 5             // 批量同步大小
    }
    
    // 初始化全局数据变更监听
    this.initGlobalDataListener()
  }

  /**
   * 注册同步监听器
   * @param {string} dataType 数据类型 (travel_plans, current_plan, ongoing_plans)
   * @param {string} identifier 标识符 (页面路径或组件ID)
   * @param {Function} callback 回调函数
   */
  registerSync(dataType, identifier, callback) {
    const key = `${dataType}_${identifier}`
    
    if (!this.syncListeners.has(dataType)) {
      this.syncListeners.set(dataType, new Map())
    }
    
    this.syncListeners.get(dataType).set(identifier, {
      callback,
      lastUpdate: 0,
      retryCount: 0
    })
    
    console.log(`注册同步监听器: ${key}`)
    
    // 启动该数据类型的同步
    this.startSync(dataType)
    
    // 立即执行一次同步
    this.performSync(dataType)
  }

  /**
   * 取消同步监听器
   * @param {string} dataType 数据类型
   * @param {string} identifier 标识符
   */
  unregisterSync(dataType, identifier) {
    if (this.syncListeners.has(dataType)) {
      this.syncListeners.get(dataType).delete(identifier)
      
      // 如果该数据类型没有监听器了，停止同步
      if (this.syncListeners.get(dataType).size === 0) {
        this.stopSync(dataType)
      }
    }
  }

  /**
   * 启动指定数据类型的同步
   * @param {string} dataType 数据类型
   */
  startSync(dataType) {
    if (this.syncTimers.has(dataType)) {
      return // 已经在同步中
    }
    
    const timer = setInterval(() => {
      this.performSync(dataType)
    }, this.config.syncInterval)
    
    this.syncTimers.set(dataType, timer)
    console.log(`启动同步: ${dataType}`)
  }

  /**
   * 停止指定数据类型的同步
   * @param {string} dataType 数据类型
   */
  stopSync(dataType) {
    if (this.syncTimers.has(dataType)) {
      clearInterval(this.syncTimers.get(dataType))
      this.syncTimers.delete(dataType)
      console.log(`停止同步: ${dataType}`)
    }
  }

  /**
   * 执行同步操作
   * @param {string} dataType 数据类型
   */
  async performSync(dataType) {
    try {
      const listeners = this.syncListeners.get(dataType)
      if (!listeners || listeners.size === 0) {
        return
      }

      // 获取最新数据
      const syncData = await this.fetchSyncData(dataType)
      
      if (!syncData.success) {
        console.error(`同步失败: ${dataType}`, syncData.error)
        return
      }

      // 检查数据是否有更新
      const currentVersion = this.dataVersions.get(dataType) || 0
      const newVersion = syncData.version || Date.now()
      
      if (newVersion <= currentVersion && !syncData.forceUpdate) {
        return // 数据没有更新
      }

      // 更新数据版本
      this.dataVersions.set(dataType, newVersion)
      
      // 通知所有监听器
      const notifyPromises = []
      listeners.forEach((listener, identifier) => {
        notifyPromises.push(this.notifyListener(dataType, identifier, listener, syncData.data))
      })
      
      await Promise.allSettled(notifyPromises)
      
      // 更新同步状态
      this.syncStatus.lastSyncTime = Date.now()
      this.syncStatus.syncCount++
      
      console.log(`同步完成: ${dataType}, 通知了 ${listeners.size} 个监听器`)
      
    } catch (error) {
      console.error(`同步异常: ${dataType}`, error)
    }
  }

  /**
   * 获取同步数据
   * @param {string} dataType 数据类型
   */
  async fetchSyncData(dataType) {
    try {
      const lastVersion = this.dataVersions.get(dataType) || 0
      
      let result
      switch (dataType) {
        case 'travel_plans':
          result = await wx.cloud.callFunction({
            name: 'travel',
            data: {
              action: 'getTravelPlans',
              data: { 
                limit: 10,
                lastVersion 
              }
            }
          })
          break
          
        case 'current_plan':
          result = await wx.cloud.callFunction({
            name: 'travel',
            data: {
              action: 'getCurrentPlan',
              data: { lastVersion }
            }
          })
          break
          
        case 'ongoing_plans':
          result = await wx.cloud.callFunction({
            name: 'travel',
            data: {
              action: 'getOngoingPlans',
              data: { lastVersion }
            }
          })
          break
          
        default:
          throw new Error(`不支持的数据类型: ${dataType}`)
      }

      if (result.result && result.result.success) {
        return {
          success: true,
          data: result.result.data,
          version: result.result.version || Date.now(),
          forceUpdate: result.result.forceUpdate || false
        }
      } else {
        throw new Error(result.result?.message || '获取数据失败')
      }
      
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 通知监听器
   * @param {string} dataType 数据类型
   * @param {string} identifier 标识符
   * @param {Object} listener 监听器对象
   * @param {any} data 数据
   */
  async notifyListener(dataType, identifier, listener, data) {
    try {
      await listener.callback(data)
      listener.lastUpdate = Date.now()
      listener.retryCount = 0
    } catch (error) {
      console.error(`通知监听器失败: ${dataType}_${identifier}`, error)
      
      // 重试机制
      listener.retryCount++
      if (listener.retryCount < this.config.maxRetries) {
        setTimeout(() => {
          this.notifyListener(dataType, identifier, listener, data)
        }, this.config.retryDelay * listener.retryCount)
      }
    }
  }

  /**
   * 手动触发同步
   * @param {string} dataType 数据类型
   * @param {boolean} forceUpdate 强制更新
   */
  async triggerSync(dataType, forceUpdate = false) {
    if (forceUpdate) {
      // 重置版本号以强制更新
      this.dataVersions.set(dataType, 0)
    }
    
    await this.performSync(dataType)
  }

  /**
   * 批量触发同步
   * @param {Array} dataTypes 数据类型数组
   * @param {boolean} forceUpdate 强制更新
   */
  async batchTriggerSync(dataTypes, forceUpdate = false) {
    const syncPromises = dataTypes.map(dataType => 
      this.triggerSync(dataType, forceUpdate)
    )
    
    await Promise.allSettled(syncPromises)
  }

  /**
   * 初始化全局数据变更监听
   */
  initGlobalDataListener() {
    // 监听全局数据变更事件
    const app = getApp()
    if (app.globalData && app.globalData.dataChangeNotifier) {
      app.globalData.dataChangeNotifier.on('travelPlanCreated', () => {
        this.batchTriggerSync(['travel_plans', 'current_plan', 'ongoing_plans'], true)
      })
      
      app.globalData.dataChangeNotifier.on('travelPlanUpdated', () => {
        this.batchTriggerSync(['travel_plans', 'current_plan', 'ongoing_plans'], true)
      })
      
      app.globalData.dataChangeNotifier.on('travelPlanDeleted', () => {
        this.batchTriggerSync(['travel_plans', 'current_plan', 'ongoing_plans'], true)
      })
      
      app.globalData.dataChangeNotifier.on('expenseRecordChanged', () => {
        this.triggerSync('travel_plans', true)
      })
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      ...this.syncStatus,
      activeDataTypes: Array.from(this.syncTimers.keys()),
      listenerCount: Array.from(this.syncListeners.values())
        .reduce((total, listeners) => total + listeners.size, 0)
    }
  }

  /**
   * 清理所有同步
   */
  cleanup() {
    // 停止所有同步定时器
    this.syncTimers.forEach((timer, dataType) => {
      clearInterval(timer)
    })
    
    // 清空所有数据
    this.syncTimers.clear()
    this.syncListeners.clear()
    this.dataVersions.clear()
    
    console.log('实时同步管理器已清理')
  }

  /**
   * 为页面提供便捷的同步方法
   * @param {Object} page 页面实例
   * @param {string} dataType 数据类型
   * @param {Function} updateCallback 更新回调
   */
  bindPageSync(page, dataType, updateCallback) {
    const pageRoute = getCurrentPages().pop().route
    
    // 注册同步
    this.registerSync(dataType, pageRoute, updateCallback)
    
    // 在页面卸载时自动清理
    const originalOnUnload = page.onUnload || function() {}
    page.onUnload = () => {
      this.unregisterSync(dataType, pageRoute)
      originalOnUnload.call(page)
    }
  }
}

// 创建全局实例
const realtimeSyncManager = new RealtimeSyncManager()

export default realtimeSyncManager

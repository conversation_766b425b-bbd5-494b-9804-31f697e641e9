// subpackages/travel-planning/plan-detail/index.js
import travelService from '../../../utils/travel-service.js'
import Dialog from '@vant/weapp/dialog/dialog'
import Toast from '@vant/weapp/toast/toast'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    planId: '',
    planData: null,
    loading: true,
    error: null,

    // 预算相关
    budgetProgress: 0,
    remainingBudget: 0,

    // 费用记录
    expenseRecords: [],
    showExpenseDetail: false,
    selectedExpense: null,

    // 预算管理
    showBudgetModal: false,
    editingBudget: {
      total: 0,
      items: []
    },

    // 操作菜单
    showActionSheet: false,
    actionSheetActions: [
      { name: '编辑计划', icon: 'edit' },
      { name: '分享计划', icon: 'share' },
      { name: '删除计划', icon: 'delete', color: '#ff4d4f' }
    ],

    // 协作相关
    collaboratorsList: [],
    isCreator: false,
    showPlanMenu: false,
    planMenuActions: [],

    // 实时状态
    editingStatus: {
      isEditing: false,
      userName: '',
      field: '',
      timestamp: null
    },

    // 同步管理
    syncTimer: null,
    lastSyncTime: 0,

    // 邀请弹窗
    showInviteModal: false,
    inviteCode: '',
    inviteExpiry: null,



    // {{ AURA-X: Modify - 更新状态映射配色. Approval: 寸止(ID:1738056000). }}
    // 状态映射
    statusMap: {
      'planning': { text: '规划中', color: '#1890ff' },
      'ongoing': { text: '进行中', color: '#52c41a' },
      'completed': { text: '已完成', color: '#722ed1' },
      'cancelled': { text: '已取消', color: '#8c8c8c' }
    },

    // {{ AURA-X: Modify - 优化状态图标为专业旅行主题. Approval: 寸止(ID:1738056000). }}
    // 状态选择器
    showStatusSelector: false,
    statusOptions: [
      {
        value: 'planning',
        text: '规划中',
        description: '正在制定旅行计划',
        color: '#1890ff',
        icon: 'travel-map'  // 地图图标，直观表示规划阶段
      },
      {
        value: 'ongoing',
        text: '进行中',
        description: '旅行正在进行',
        color: '#52c41a',
        icon: 'travel-plane'  // 专业飞机图标，完美契合旅行主题
      },
      {
        value: 'completed',
        text: '已完成',
        description: '旅行已经结束',
        color: '#722ed1',
        icon: 'travel-stars'  // 星空图标，表示完成的纪念意义
      },
      {
        value: 'cancelled',
        text: '已取消',
        description: '旅行计划已取消',
        color: '#8c8c8c',
        icon: 'alert-triangle'  // 警告图标，明确表示取消状态
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.id) {
      this.setData({ planId: options.id })
      this.loadPlanDetail()

      // 初始化协作功能
      this.initCollaboration()
    } else {
      this.setData({
        error: '缺少计划ID参数',
        loading: false
      })
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新数据
    if (this.data.planId) {
      this.loadPlanDetail()
      this.checkForUpdates()
    }
  },

  onHide() {
    // 页面隐藏时停止同步
    this.stopSync()
  },

  onUnload() {
    // 页面卸载时清理定时器
    this.stopSync()
  },



  /**
   * 加载计划详情
   */
  async loadPlanDetail() {
    try {
      this.setData({ loading: true, error: null })

      const result = await travelService.getTravelPlan(this.data.planId)

      if (result.success) {
        const planData = result.data

        // 计算预算相关数据 - 兼容新旧数据格式
        const totalBudget = planData.budgetDetail?.total || planData.budget || 0
        const spentBudget = planData.budgetDetail?.spent || 0
        const budgetProgress = totalBudget > 0 ? Math.round((spentBudget / totalBudget) * 100) : 0
        const remainingBudget = totalBudget - spentBudget

        this.setData({
          planData,
          budgetProgress,
          remainingBudget,
          loading: false
        })

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: planData.title || '计划详情'
        })

        // 加载费用记录
        this.loadExpenseRecords()

        // 处理协作数据
        await this.processCollaborationData(planData)

        // {{ AURA-X: Add - 自动更新协作者信息. Approval: 寸止(ID:1738056000). }}
        // 如果当前用户是协作者，更新协作者信息
        this.updateCollaboratorInfoIfNeeded(planData)

      } else {
        this.setData({
          error: result.message || '加载失败',
          loading: false
        })
        Toast.fail(result.message || '加载计划详情失败')
      }
    } catch (error) {
      this.setData({
        error: '网络异常，请重试',
        loading: false
      })
      Toast.fail('网络异常，请重试')
    }
  },

  /**
   * {{ AURA-X: Modify - 添加调试信息追踪费用加载问题. Approval: 寸止(ID:1738056000). }}
   * 加载费用记录
   */
  async loadExpenseRecords() {
    try {
      if (!this.data.planId) {
        return
      }

      // 调用数据管理器获取真实数据
      const dataManager = require('../../../utils/data-manager.js').default

      // 获取与当前旅行计划相关的费用记录
      const result = await dataManager.getExpenseRecords({
        mode: 'travel',
        planId: this.data.planId,
        limit: 5, // 只显示最近5条
        orderBy: 'createTime',
        orderDirection: 'desc'
      })

      if (result.success && result.data) {
        // 格式化数据以适应页面显示
        const formattedRecords = result.data.map(record => ({
          id: record._id,
          amount: record.amount,
          category: record.category?.main || '其他',
          description: record.description || record.category?.main || '支出',
          date: this.formatDate(record.createTime),
          location: record.location?.name || ''
        }))

        // 计算总花费
        const totalSpent = formattedRecords.reduce((sum, record) => sum + record.amount, 0)

        // 更新预算数据
        const planData = this.data.planData
        if (planData) {
          const totalBudget = planData.budgetDetail?.total || planData.budget || 0
          const budgetProgress = totalBudget > 0 ? Math.round((totalSpent / totalBudget) * 100) : 0
          const remainingBudget = totalBudget - totalSpent

          this.setData({
            expenseRecords: formattedRecords,
            budgetProgress,
            remainingBudget
          })

          // 同步更新旅行计划的花费数据
          this.updatePlanSpentAmount(totalSpent)
        } else {
          this.setData({
            expenseRecords: formattedRecords
          })
        }
      } else {
        this.setData({
          expenseRecords: []
        })
      }
    } catch (error) {
      this.setData({
        expenseRecords: []
      })
    }
  },

  /**
   * 格式化日期显示
   */
  formatDate(dateInput) {
    try {
      let date
      if (dateInput instanceof Date) {
        date = dateInput
      } else if (typeof dateInput === 'string') {
        date = new Date(dateInput)
      } else if (dateInput && dateInput.$date) {
        // 处理云数据库的日期格式
        date = new Date(dateInput.$date)
      } else {
        date = new Date()
      }

      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${month}-${day}`
    } catch (error) {
      return '今天'
    }
  },

  /**
   * 记录费用
   */
  recordExpense() {
    if (!this.data.planData) return

    wx.navigateTo({
      url: `/subpackages/account/travel-expense/index?mode=travel&planId=${this.data.planId}`
    })
  },

  /**
   * 编辑计划
   */
  editPlan() {
    if (!this.data.planData) return

    wx.navigateTo({
      url: `/subpackages/travel-planning/create-plan/index?mode=edit&id=${this.data.planId}`
    })
  },

  /**
   * 查看行程
   */
  viewItinerary() {
    if (!this.data.planData) return

    wx.navigateTo({
      url: `/subpackages/travel-planning/itinerary/index?planId=${this.data.planId}`
    })
  },

  /**
   * 分享计划
   */
  sharePlan() {
    if (!this.data.planData) return

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })

    Toast.success('可以通过右上角菜单分享')
  },

  /**
   * 显示操作菜单
   */
  showActionMenu() {
    this.setData({ showActionSheet: true })
  },

  /**
   * 关闭操作菜单
   */
  closeActionSheet() {
    this.setData({ showActionSheet: false })
  },

  /**
   * 处理操作菜单选择
   */
  onActionSelect(event) {
    const { name } = event.detail
    this.setData({ showActionSheet: false })

    switch (name) {
      case '编辑计划':
        this.editPlan()
        break
      case '分享计划':
        this.sharePlan()
        break
      case '删除计划':
        this.confirmDeletePlan()
        break
    }
  },

  /**
   * 确认删除计划
   */
  confirmDeletePlan() {
    Dialog.confirm({
      title: '确认删除',
      message: '删除后无法恢复，确定要删除这个旅行计划吗？',
      confirmButtonColor: '#ff4d4f'
    }).then(() => {
      this.deletePlan()
    }).catch(() => {
      // 用户取消删除
    })
  },

  /**
   * 删除计划
   */
  async deletePlan() {
    try {
      Toast.loading({ message: '删除中...', forbidClick: true })

      const result = await travelService.deleteTravelPlan(this.data.planId)

      if (result.success) {
        Toast.success('删除成功')

        // 通知旅行规划页面刷新数据
        this.notifyTravelPageRefresh()

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        Toast.fail(result.message || '删除失败')
      }
    } catch (error) {
      Toast.fail('网络异常，请重试')
    }
  },

  /**
   * 查看费用详情
   */
  viewExpenseDetail(event) {
    const { record } = event.currentTarget.dataset
    if (!record) return

    // 显示费用详情弹窗
    this.setData({
      showExpenseDetail: true,
      selectedExpense: record
    })
  },

  /**
   * 关闭费用详情
   */
  closeExpenseDetail() {
    this.setData({
      showExpenseDetail: false,
      selectedExpense: null
    })
  },

  /**
   * {{ AURA-X: Modify - 使用统一删除处理器，提升安卓兼容性. Approval: 寸止(ID:1738056000). }}
   * 删除费用记录
   */
  async deleteExpenseRecord(e) {
    const record = e.currentTarget.dataset.record
    if (!record) return

    try {
      // 使用统一删除处理器
      const unifiedDeleteHandler = require('../../../utils/unified-delete-handler.js').default

      const result = await unifiedDeleteHandler.deleteExpenseRecord(record.id, {
        confirmContent: `确定要删除这条费用记录吗？\n${record.description} ¥${record.amount}`,
        onSuccess: async () => {
          // 重新加载费用记录
          await this.loadExpenseRecords()
        },
        onError: (error) => {
          console.error('删除费用记录失败:', error)
        }
      })

      // 如果用户取消删除，不需要额外处理
      if (result.cancelled) {
        return
      }

    } catch (error) {
      console.error('删除费用记录异常:', error)
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 查看全部费用
   */
  viewAllExpenses() {
    if (!this.data.planData) return

    wx.navigateTo({
      url: `/subpackages/travel-planning/expense-list/index?planId=${this.data.planId}&planTitle=${encodeURIComponent(this.data.planData.title || '旅行费用')}`
    })
  },

  /**
   * 管理预算
   */
  manageBudget() {
    if (!this.data.planData) return

    // 初始化编辑数据
    const planData = this.data.planData
    const budgetDetail = planData.budgetDetail || planData.budget || {}

    this.setData({
      showBudgetModal: true,
      editingBudget: {
        total: budgetDetail.total || planData.budget || 0,
        items: JSON.parse(JSON.stringify(budgetDetail.items || []))
      }
    })
  },

  /**
   * 关闭预算管理弹窗
   */
  closeBudgetModal() {
    this.setData({ showBudgetModal: false })
  },

  /**
   * 删除费用记录
   */
  async deleteExpenseRecord(event) {
    const { record } = event.currentTarget.dataset
    if (!record || !record.id) {
      wx.showToast({
        title: '记录信息错误',
        icon: 'none'
      })
      return
    }

    // 确认删除
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '确认删除',
        content: `确定要删除这条费用记录吗？\n${record.description} ¥${record.amount}`,
        confirmText: '删除',
        confirmColor: '#ff4d4f',
        success: (res) => resolve(res.confirm),
        fail: () => resolve(false)
      })
    })

    if (!result) return

    try {
      wx.showLoading({ title: '删除中...' })

      const dataManager = require('../../../utils/data-manager.js').default
      const deleteResult = await dataManager.deleteExpenseRecord(record.id)

      if (deleteResult.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })

        // 重新加载费用记录
        this.loadExpenseRecords()

        // 重新加载计划详情以更新预算信息
        this.loadPlanDetail()
      } else {
        throw new Error(deleteResult.message || '删除失败')
      }
    } catch (error) {
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 总预算输入
   */
  onTotalBudgetInput(event) {
    const value = parseFloat(event.detail.value) || 0
    this.setData({
      'editingBudget.total': value
    })
  },

  /**
   * 添加预算项目
   */
  addBudgetItem() {
    const items = this.data.editingBudget.items
    items.push({
      name: '',
      amount: 0
    })
    this.setData({
      'editingBudget.items': items
    })
  },

  /**
   * 删除预算项目
   */
  deleteBudgetItem(event) {
    const index = event.currentTarget.dataset.index
    const items = this.data.editingBudget.items
    items.splice(index, 1)
    this.setData({
      'editingBudget.items': items
    })
  },

  /**
   * 项目名称输入
   */
  onItemNameInput(event) {
    const index = event.currentTarget.dataset.index
    const value = event.detail.value
    const items = this.data.editingBudget.items
    items[index].name = value
    this.setData({
      'editingBudget.items': items
    })
  },

  /**
   * 项目金额输入
   */
  onItemAmountInput(event) {
    const index = event.currentTarget.dataset.index
    const value = parseFloat(event.detail.value) || 0
    const items = this.data.editingBudget.items
    items[index].amount = value
    this.setData({
      'editingBudget.items': items
    })
  },

  /**
   * 保存预算
   */
  async saveBudget() {
    try {
      const editingBudget = this.data.editingBudget

      // 验证数据
      if (editingBudget.total <= 0) {
        Toast.fail('请输入有效的总预算')
        return
      }

      // 过滤空项目
      const validItems = editingBudget.items.filter(item =>
        item.name.trim() && item.amount > 0
      )

      // 构建更新数据
      const updateData = {
        budget: editingBudget.total, // 兼容旧格式
        budgetDetail: {
          total: editingBudget.total,
          spent: this.data.planData.budgetDetail?.spent || 0,
          items: validItems
        }
      }

      Toast.loading({ message: '保存中...', forbidClick: true })

      const result = await travelService.updateTravelPlan(this.data.planId, updateData)

      if (result.success) {
        Toast.success('保存成功')
        this.setData({ showBudgetModal: false })

        // 刷新页面数据
        setTimeout(() => {
          this.loadPlanDetail()
        }, 1000)
      } else {
        Toast.fail(result.message || '保存失败')
      }
    } catch (error) {
      Toast.fail('网络异常，请重试')
    }
  },

  /**
   * 更新旅行计划的花费数据
   */
  async updatePlanSpentAmount(totalSpent) {
    try {
      if (!this.data.planId || !this.data.planData) return

      // 检查是否需要更新
      const currentSpent = this.data.planData.budgetDetail?.spent || 0
      if (Math.abs(currentSpent - totalSpent) < 0.01) return // 如果差异很小，不更新

      // 构建更新数据
      const updateData = {
        budgetDetail: {
          ...this.data.planData.budgetDetail,
          spent: totalSpent
        }
      }

      // 静默更新，不显示加载状态
      const result = await travelService.updateTravelPlan(this.data.planId, updateData)

      if (result.success) {
        // 更新本地数据
        const planData = {...this.data.planData}
        if (!planData.budgetDetail) {
          planData.budgetDetail = {
            total: planData.budget || 0,
            spent: totalSpent,
            items: []
          }
        } else {
          planData.budgetDetail.spent = totalSpent
        }

        this.setData({ planData })
      }
    } catch (error) {
      console.error('更新旅行计划花费数据失败:', error)
      // 静默失败，不影响用户体验
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadPlanDetail().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 分享给好友
   */
  onShareAppMessage() {
    const planData = this.data.planData
    if (!planData) return {}

    return {
      title: `${planData.title} - 我的旅行计划`,
      path: `/subpackages/travel-planning/plan-detail/index?id=${this.data.planId}`,
      imageUrl: '/images/airplane.svg'
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const planData = this.data.planData
    if (!planData) return {}

    return {
      title: `${planData.title} - 我的旅行计划`,
      query: `id=${this.data.planId}`,
      imageUrl: '/images/airplane.svg'
    }
  },

  /**
   * 显示状态选择器
   */
  showStatusSelector() {
    this.setData({ showStatusSelector: true })

    // 触觉反馈
    wx.vibrateShort()
  },

  /**
   * 关闭状态选择器
   */
  closeStatusSelector() {
    this.setData({ showStatusSelector: false })
  },

  /**
   * 选择状态
   */
  async selectStatus(e) {
    const newStatus = e.currentTarget.dataset.status
    const currentStatus = this.data.planData.status

    if (newStatus === currentStatus) {
      this.closeStatusSelector()
      return
    }

    // 显示确认对话框
    const statusOption = this.data.statusOptions.find(option => option.value === newStatus)

    wx.showModal({
      title: '确认修改状态',
      content: `确定要将计划状态修改为"${statusOption.text}"吗？`,
      confirmText: '确定',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.updatePlanStatus(newStatus)
        }
      }
    })
  },

  /**
   * 更新计划状态
   */
  async updatePlanStatus(newStatus) {
    try {
      Toast.loading({ message: '更新中...', forbidClick: true })

      const result = await travelService.updateTravelPlan(this.data.planId, {
        status: newStatus
      })

      if (result.success) {
        // 更新本地数据
        this.setData({
          'planData.status': newStatus,
          showStatusSelector: false
        })

        Toast.success('状态更新成功')

        // 触觉反馈
        wx.vibrateShort()

        // 刷新页面数据以确保同步
        setTimeout(() => {
          this.loadPlanDetail()
        }, 1000)
      } else {
        Toast.fail(result.message || '更新失败')
      }
    } catch (error) {
      Toast.fail('网络异常，请重试')
    }
  },

  // 通知旅行规划页面刷新
  notifyTravelPageRefresh() {
    try {
      // 使用data-manager的缓存清理方法
      const dataManager = require('../../../utils/data-manager.js').default
      dataManager.clearTravelPageCache()

      // 设置全局刷新标记
      wx.setStorageSync('travel_page_needs_refresh', Date.now())
    } catch (error) {
      // 静默处理通知失败
    }
  },

  // ==================== 协作功能相关方法 ====================

  // 初始化协作功能
  initCollaboration() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo && userInfo.openid) {
      this.setData({
        currentUserOpenid: userInfo.openid
      })
    }

    // 开始同步检查
    this.startSync()
  },

  // 处理协作数据
  async processCollaborationData(planData) {
    const currentUserOpenid = this.data.currentUserOpenid
    const isCreator = planData._openid === currentUserOpenid

    // 构建协作者列表（包含创建者）
    let collaboratorsList = []

    // 添加创建者
    collaboratorsList.push({
      openid: planData._openid,
      nickname: planData.creatorInfo?.nickname || '创建者',
      avatar: planData.creatorInfo?.avatar || '/images/user.svg',
      isCreator: true,
      joinTime: planData.createTime
    })

    // 添加协作者
    if (planData.collaboration?.collaborators) {
      collaboratorsList = collaboratorsList.concat(
        planData.collaboration.collaborators.map(c => ({
          ...c,
          isCreator: false
        }))
      )
    }

    // 获取所有用户的临时头像链接
    for (let i = 0; i < collaboratorsList.length; i++) {
      const collaborator = collaboratorsList[i]
      if (collaborator.avatar && collaborator.avatar.startsWith('cloud://')) {
        try {
          const result = await wx.cloud.callFunction({
            name: 'travel',
            data: {
              action: 'getAvatarProxy',
              data: { fileID: collaborator.avatar }
            }
          })

          if (result.result.success) {
            collaboratorsList[i].tempAvatarUrl = result.result.data.tempFileURL
          }
        } catch (error) {
          console.error('获取头像代理失败:', error)
        }
      }
    }

    // 构建长按菜单
    let planMenuActions = [
      { name: '编辑计划', value: 'edit', icon: 'edit' }
    ]

    if (planData.collaboration?.enabled) {
      planMenuActions.push({ name: '邀请协作', value: 'invite', icon: 'friends' })
      if (isCreator) {
        planMenuActions.push({ name: '管理协作', value: 'manage', icon: 'settings' })
      }
    } else if (isCreator) {
      planMenuActions.push({ name: '开启协作', value: 'enable', icon: 'friends' })
    }

    if (isCreator) {
      planMenuActions.push({ name: '删除计划', value: 'delete', icon: 'delete', color: '#ff4d4f' })
    }

    this.setData({
      collaboratorsList,
      isCreator,
      planMenuActions,
      lastSyncTime: Date.now()
    })
  },

  // {{ AURA-X: Add - 更新协作者信息方法. Approval: 寸止(ID:1738056000). }}
  // 如果需要，更新协作者信息
  async updateCollaboratorInfoIfNeeded(planData) {
    const currentUserOpenid = this.data.currentUserOpenid
    const isCollaborator = planData.collaboration?.collaborators?.some(c => c.openid === currentUserOpenid)

    // 如果当前用户是协作者且头像为空，则更新信息
    if (isCollaborator) {
      const collaborator = planData.collaboration.collaborators.find(c => c.openid === currentUserOpenid)
      if (!collaborator.avatar || collaborator.avatar === '') {
        try {
          await wx.cloud.callFunction({
            name: 'travel',
            data: {
              action: 'updateCollaboratorInfo',
              data: { planId: this.data.planId }
            }
          })

          // 更新成功后重新加载计划详情
          setTimeout(() => {
            this.loadPlanDetail()
          }, 1000)
        } catch (error) {
          console.error('更新协作者信息失败:', error)
        }
      }
    }
  },

  // 显示长按菜单
  showPlanMenu() {
    wx.vibrateShort() // 触觉反馈
    this.setData({ showPlanMenu: true })
  },

  // 处理长按菜单选择
  async onPlanMenuSelect(e) {
    const { value } = e.detail
    this.setData({ showPlanMenu: false })

    switch (value) {
      case 'edit':
        this.editPlan()
        break
      case 'enable':
        await this.enableCollaboration()
        break
      case 'invite':
        this.showInviteModal()
        break
      case 'manage':
        this.manageCollaborators()
        break
      case 'delete':
        this.deletePlan()
        break
    }
  },

  // 关闭长按菜单
  closePlanMenu() {
    this.setData({ showPlanMenu: false })
  },

  // 开始同步检查
  startSync() {
    if (this.data.syncTimer) {
      clearInterval(this.data.syncTimer)
    }

    // 每30秒检查一次更新
    const timer = setInterval(() => {
      this.checkForUpdates()
    }, 30000)

    this.setData({ syncTimer: timer })
  },

  // 停止同步
  stopSync() {
    if (this.data.syncTimer) {
      clearInterval(this.data.syncTimer)
      this.setData({ syncTimer: null })
    }
  },

  // 检查更新
  async checkForUpdates() {
    if (!this.data.planId || !this.data.lastSyncTime) {
      return
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'travel',
        data: {
          action: 'checkPlanUpdates',
          data: {
            planId: this.data.planId,
            lastSyncTime: this.data.lastSyncTime
          }
        }
      })

      if (result.result.success && result.result.data.hasUpdates) {
        const updates = result.result.data.updates

        // 更新计划数据
        this.setData({
          planData: updates.planData,
          lastSyncTime: Date.now()
        })

        // 处理协作数据
        await this.processCollaborationData(updates.planData)

        // 显示更新提示
        if (updates.recentLogs && updates.recentLogs.length > 0) {
          const latestLog = updates.recentLogs[0]
          this.showEditingStatus(latestLog.operatorName, latestLog.action)
        }
      }
    } catch (error) {
      console.error('检查更新失败:', error)
    }
  },

  // 显示编辑状态
  showEditingStatus(userName, field) {
    this.setData({
      editingStatus: {
        isEditing: true,
        userName: userName,
        field: field,
        timestamp: Date.now()
      }
    })

    // 3秒后自动隐藏
    setTimeout(() => {
      this.setData({
        'editingStatus.isEditing': false
      })
    }, 3000)
  },

  // 开启协作功能
  async enableCollaboration() {
    try {
      wx.showLoading({ title: '开启中...' })

      const result = await wx.cloud.callFunction({
        name: 'travel',
        data: {
          action: 'enableCollaboration',
          data: { planId: this.data.planId }
        }
      })

      wx.hideLoading()

      if (result.result.success) {
        wx.showToast({
          title: '协作功能已开启',
          icon: 'success'
        })

        // 刷新计划数据
        this.loadPlanDetail()
      } else {
        wx.showToast({
          title: result.result.message || '开启失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '开启失败，请重试',
        icon: 'none'
      })
    }
  },

  // 显示邀请弹窗
  showInviteModal() {
    const { planData } = this.data

    if (planData.collaboration?.enabled) {
      this.setData({
        showInviteModal: true,
        inviteCode: planData.collaboration.inviteCode,
        inviteExpiry: planData.collaboration.inviteExpiry
      })
    } else {
      wx.showToast({
        title: '请先开启协作功能',
        icon: 'none'
      })
    }
  },

  // 关闭邀请弹窗
  closeInviteModal() {
    this.setData({ showInviteModal: false })
  },

  // 复制邀请码
  copyInviteCode() {
    const { inviteCode } = this.data

    wx.setClipboardData({
      data: inviteCode,
      success: () => {
        wx.showToast({
          title: '邀请码已复制',
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
  },



  // 复制邀请链接
  copyInviteLink() {
    const { planData, inviteCode } = this.data
    const planTitle = planData ? planData.title : '旅行计划'
    const link = `🎒 邀请你一起规划旅行：${planTitle}

📋 邀请码：${inviteCode}

💡 使用方法：
1. 打开"爱巢小记"小程序
2. 在旅行规划页面点击"加入计划"
3. 输入邀请码即可加入协作

快来一起规划精彩的旅程吧！✨`

    wx.setClipboardData({
      data: link,
      success: () => {
        wx.showToast({
          title: '邀请链接已复制',
          icon: 'success'
        })
        this.closeInviteModal()
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
  },

  // 管理协作者
  manageCollaborators() {
    // 跳转到协作者管理页面
    wx.navigateTo({
      url: `/subpackages/travel-planning/collaborators/index?planId=${this.data.planId}`
    })
  },

  // {{ AURA-X: Modify - 修复编辑计划路径，统一使用create-plan页面的编辑模式. Approval: 寸止(ID:1738056000). }}
  // 编辑计划
  editPlan() {
    if (!this.data.planData) return

    wx.navigateTo({
      url: `/subpackages/travel-planning/create-plan/index?mode=edit&id=${this.data.planId}`
    })
  },

  // 删除计划
  deletePlan() {
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这个旅行计划吗？',
      confirmText: '删除',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.confirmDeletePlan()
        }
      }
    })
  },

  // {{ AURA-X: Modify - 使用统一删除处理器删除计划. Approval: 寸止(ID:1738056000). }}
  // 确认删除计划
  async confirmDeletePlan() {
    try {
      // 使用统一删除处理器
      const unifiedDeleteHandler = require('../../../utils/unified-delete-handler.js').default

      const result = await unifiedDeleteHandler.deleteTravelPlan(this.data.planId, {
        showConfirm: false, // 已经在 deletePlan 方法中确认过了
        onSuccess: async () => {
          // 清理相关缓存
          const dataManager = require('../../../utils/data-manager.js').default
          dataManager.clearTravelPageCache()

          // 返回上一页
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        },
        onError: (error) => {
          console.error('删除计划失败:', error)
        }
      })

    } catch (error) {
      console.error('删除计划异常:', error)
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      })
    }
  }
})